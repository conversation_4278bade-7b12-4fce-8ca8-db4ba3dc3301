/**
 * 🤖 Modal de Sugestão Inteligente da IA
 * 
 * Modal que apresenta análise personalizada da DeepSeek AI
 * baseada nos dados reais da obra e estimativa SINAPI.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Bot,
  Lightbulb,
  TrendingUp,
  AlertTriangle,
  Calculator,
  Clock,
  CheckCircle,
  X,
  Loader2,
  DollarSign,
  Building,
  MapPin
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { formatCurrencyBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";

// ====================================
// 🎯 TIPOS E INTERFACES
// ====================================

export interface DadosObraReal {
  id: string;
  nome: string;
  tipo_projeto: string;
  area_total: number;
  endereco: string;
  estado: string;
  cidade?: string;
  orcamento: number;
  valor_orcamento_parametrico: number;
  percentual_diferenca: number;
  padrao_obra?: string;
  tipo_condominio?: string;
}

export interface AnaliseIA {
  problema_identificado: string;
  impacto_financeiro: string;
  sugestoes_praticas: string[];
  cronograma_acao: {
    item: string;
    prazo: string;
    prioridade: 'alta' | 'media' | 'baixa';
  }[];
  alternativas_viabilidade: string[];
  valor_recomendado_minimo: number;
  valor_recomendado_otimo: number;
  observacoes_tecnicas: string[];
}

export interface ModalSugestaoIAProps {
  isOpen: boolean;
  onClose: () => void;
  dadosObra: DadosObraReal;
  onAplicarSugestao?: (valorSugerido: number) => void;
}

// ====================================
// 🎯 COMPONENTE PRINCIPAL
// ====================================

export const ModalSugestaoIA: React.FC<ModalSugestaoIAProps> = ({
  isOpen,
  onClose,
  dadosObra,
  onAplicarSugestao
}) => {
  const [analise, setAnalise] = useState<AnaliseIA | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ====================================
  // 🤖 FUNÇÃO PARA CHAMAR A IA
  // ====================================

  const solicitarAnaliseIA = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log("🤖 Enviando dados reais da obra para análise IA:", {
        obra_id: dadosObra.id,
        nome: dadosObra.nome,
        percentual_diferenca: dadosObra.percentual_diferenca
      });

      // Usar edge function do Supabase para análise IA
      const { data, error } = await supabase.functions.invoke('sugestao-orcamento-ia', {
        body: {
          dados_obra_reais: {
            id: dadosObra.id,
            nome: dadosObra.nome,
            tipo_projeto: dadosObra.tipo_projeto,
            area_total: dadosObra.area_total,
            localizacao: {
              endereco: dadosObra.endereco,
              estado: dadosObra.estado,
              cidade: dadosObra.cidade
            },
            financeiro: {
              orcamento_atual: dadosObra.orcamento,
              estimativa_sinapi: dadosObra.valor_orcamento_parametrico,
              diferenca_percentual: dadosObra.percentual_diferenca
            },
            especificacoes: {
              padrao_obra: dadosObra.padrao_obra,
              tipo_condominio: dadosObra.tipo_condominio
            }
          }
        }
      });

      if (error) {
        throw new Error(error.message || 'Erro na comunicação com a IA');
      }

      if (data && data.success) {
        setAnalise(data.data);
        console.log("✅ Análise IA recebida com sucesso");
      } else {
        throw new Error(data?.error || "Erro desconhecido na análise");
      }

    } catch (err: any) {
      console.error("❌ Erro na análise IA:", err);
      setError(err.message || "Falha na comunicação com a IA");
    } finally {
      setIsLoading(false);
    }
  };

  // Iniciar análise quando modal abre
  React.useEffect(() => {
    if (isOpen && !analise && !isLoading) {
      solicitarAnaliseIA();
    }
  }, [isOpen]);

  // ====================================
  // 🎨 HELPERS DE RENDERIZAÇÃO
  // ====================================

  const getPrioridadeColor = (prioridade: string) => {
    switch (prioridade) {
      case 'alta': return 'bg-red-500';
      case 'media': return 'bg-yellow-500';
      case 'baixa': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const handleAplicarValor = (valor: number) => {
    if (onAplicarSugestao) {
      onAplicarSugestao(valor);
    }
    onClose();
  };

  // ====================================
  // 🎯 RENDER
  // ====================================

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600">
              <Bot className="h-5 w-5 text-white" />
            </div>
            Análise Inteligente de Orçamento
          </DialogTitle>
          <DialogDescription>
            Análise personalizada baseada nos dados reais da obra <strong>{dadosObra.nome}</strong>
          </DialogDescription>
        </DialogHeader>

        {/* Dados da Obra */}
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Building className="h-4 w-4" />
              Dados da Obra
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Área:</span> {dadosObra.area_total?.toLocaleString()} m²
              </div>
              <div>
                <span className="font-medium">Tipo:</span> {dadosObra.tipo_projeto}
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{dadosObra.estado}</span>
              </div>
              <div>
                <span className="font-medium">Orçamento:</span> {formatCurrencyBR(dadosObra.orcamento)}
              </div>
              <div>
                <span className="font-medium">Est. SINAPI:</span> {formatCurrencyBR(dadosObra.valor_orcamento_parametrico)}
              </div>
              <div>
                <Badge variant="destructive" className="text-xs">
                  {dadosObra.percentual_diferenca.toFixed(1)}% diferença
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Conteúdo da Análise */}
        <AnimatePresence mode="wait">
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex flex-col items-center justify-center py-12"
            >
              <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
              <p className="text-muted-foreground">Analisando dados reais da obra...</p>
              <p className="text-sm text-muted-foreground mt-1">Conectando com DeepSeek AI</p>
            </motion.div>
          )}

          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <Card className="border-red-200 bg-red-50 dark:bg-red-950/30">
                <CardContent className="pt-6">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-600" />
                    <div>
                      <h4 className="font-medium text-red-800 dark:text-red-200">
                        Erro na Análise
                      </h4>
                      <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                        {error}
                      </p>
                    </div>
                  </div>
                  <Button 
                    onClick={solicitarAnaliseIA} 
                    variant="outline" 
                    size="sm" 
                    className="mt-4"
                  >
                    Tentar Novamente
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {analise && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-6"
            >
              {/* Problema Identificado */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                    Problema Identificado
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{analise.problema_identificado}</p>
                  <div className="mt-3 p-3 bg-red-50 dark:bg-red-950/30 rounded-lg">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      Impacto Financeiro:
                    </p>
                    <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                      {analise.impacto_financeiro}
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Sugestões Práticas */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Lightbulb className="h-5 w-5 text-yellow-500" />
                    Sugestões Práticas
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {analise.sugestoes_praticas.map((sugestao, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        {sugestao}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              {/* Valores Recomendados */}
              <Card className="border-green-200 bg-green-50 dark:bg-green-950/30">
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    Valores Recomendados
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm text-muted-foreground">Mínimo Viável</p>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrencyBR(analise.valor_recomendado_minimo)}
                      </p>
                      <Button 
                        onClick={() => handleAplicarValor(analise.valor_recomendado_minimo)}
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                      >
                        Aplicar Valor
                      </Button>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm text-muted-foreground">Recomendado Ótimo</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {formatCurrencyBR(analise.valor_recomendado_otimo)}
                      </p>
                      <Button 
                        onClick={() => handleAplicarValor(analise.valor_recomendado_otimo)}
                        size="sm" 
                        className="mt-2"
                      >
                        Aplicar Valor
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Cronograma de Ação */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Clock className="h-5 w-5 text-blue-500" />
                    Cronograma de Ação
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analise.cronograma_acao.map((item, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                        <div className={cn(
                          "w-3 h-3 rounded-full",
                          getPrioridadeColor(item.prioridade)
                        )} />
                        <div className="flex-1">
                          <p className="text-sm font-medium">{item.item}</p>
                          <p className="text-xs text-muted-foreground">{item.prazo}</p>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {item.prioridade}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Observações Técnicas */}
              {analise.observacoes_tecnicas.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Calculator className="h-5 w-5 text-purple-500" />
                      Observações Técnicas
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {analise.observacoes_tecnicas.map((obs, index) => (
                        <li key={index} className="text-sm text-muted-foreground">
                          • {obs}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Footer com botões */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Fechar
          </Button>
          {analise && (
            <Button onClick={solicitarAnaliseIA} variant="outline">
              <Bot className="h-4 w-4 mr-2" />
              Nova Análise
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ModalSugestaoIA;