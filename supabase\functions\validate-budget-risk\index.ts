import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

// ====================================
// 🎯 INTERFACES
// ====================================

interface DadosObra {
  tipo_obra?: string;
  padrao_obra?: string;
  area_total?: number;
  area_construida?: number;
  estado?: string;
  cidade?: string;
  tipo_condominio?: "VERTICAL" | "HORIZONTAL";
  numero_blocos?: number;
  andares_por_bloco?: number;
  unidades_por_andar?: number;
  numero_unidades?: number;
}

interface RequestData {
  dados_obra: DadosObra;
  orcamento_usuario: number;
}

interface ResponseData {
  estimated_cost: number;
  cost_per_m2: number;
  calculation_params: {
    tipo_obra: string;
    padrao_obra: string;
    area_calculada: number;
    multiplicador_regional: number;
    fator_complexidade: number;
    versao_calculo: string;
  };
  risk_analysis: {
    percentage_difference: number;
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    is_underbudgeted: boolean;
    suggested_minimum: number;
  };
}

// ====================================
// 🏗️ FUNÇÃO DE CÁLCULO RÁPIDO
// ====================================

/**
 * Cálculo simplificado e otimizado baseado na Edge Function ai-calculate-budget-v12
 * Foco em performance para validação em tempo real
 */
function calcularEstimativaRapida(dadosObra: DadosObra): {
  custoEstimado: number;
  custoM2: number;
  parametros: any;
} {
  // Validação básica
  const areaCalculada = dadosObra.area_construida || dadosObra.area_total || 100;
  if (areaCalculada <= 0) {
    throw new Error("Área deve ser maior que zero");
  }

  const tipoObra = dadosObra.tipo_obra || "R1_UNIFAMILIAR";
  const padraoObra = dadosObra.padrao_obra || "NORMAL";
  const estado = dadosObra.estado || "GO";

  // ===== CUSTO BASE POR PADRÃO =====
  let custoM2Base = 1800; // NORMAL
  switch (padraoObra) {
    case "ALTO":
      custoM2Base = 2500;
      break;
    case "BAIXO":
      custoM2Base = 1200;
      break;
    case "LUXO":
      custoM2Base = 3500;
      break;
    case "POPULAR":
      custoM2Base = 1200;
      break;
  }

  // ===== MULTIPLICADOR REGIONAL =====
  const multiplicadorRegional = 
    estado === "SP" ? 1.2 :
    estado === "RJ" ? 1.15 :
    estado === "GO" ? 0.9 :
    estado === "MG" ? 0.95 :
    estado === "SC" ? 1.05 :
    estado === "PR" ? 1.0 :
    estado === "RS" ? 1.05 :
    estado === "BA" ? 0.85 :
    estado === "PE" ? 0.8 :
    estado === "CE" ? 0.75 :
    estado === "DF" ? 1.1 : 1.0;

  // ===== FATOR DE COMPLEXIDADE POR TIPO DE OBRA =====
  let fatorComplexidade = 1.0;
  let custoM2Ajustado = custoM2Base;

  switch (tipoObra) {
    case "R4_MULTIFAMILIAR":
      if (dadosObra.tipo_condominio === "VERTICAL") {
        // Prédios/Torres - mais complexo
        fatorComplexidade = 1.45;
        custoM2Ajustado = custoM2Base * 1.2; // Base maior para verticais
      } else if (dadosObra.tipo_condominio === "HORIZONTAL") {
        // Casas/Lotes - complexidade média
        fatorComplexidade = 1.25;
        custoM2Ajustado = custoM2Base * 1.05; // Base ligeiramente maior
      } else {
        // Padrão multifamiliar
        fatorComplexidade = 1.35;
        custoM2Ajustado = custoM2Base * 1.1;
      }
      break;

    case "COMERCIAL_GALPAO":
      // Galpões - estrutura mais simples
      fatorComplexidade = 0.8;
      custoM2Ajustado = custoM2Base * 0.7; // Base menor para galpões
      break;

    case "R1_UNIFAMILIAR":
    default:
      // Residencial unifamiliar - padrão
      fatorComplexidade = 1.0;
      custoM2Ajustado = custoM2Base;
      break;
  }

  // ===== FATORES DE PADRÃO ESPECÍFICOS =====
  const fatoresPadrao: Record<string, number> = {
    POPULAR: 1.0,
    NORMAL: tipoObra === "R4_MULTIFAMILIAR" ? 1.3 : 1.0,
    ALTO: tipoObra === "R4_MULTIFAMILIAR" ? 1.8 : 1.4,
    LUXO: tipoObra === "R4_MULTIFAMILIAR" ? 2.3 : 1.8,
  };

  const fatorPadrao = fatoresPadrao[padraoObra] || 1.0;

  // ===== CÁLCULO FINAL =====
  const custoTotalM2 = custoM2Ajustado * multiplicadorRegional * fatorComplexidade * fatorPadrao;
  const custoEstimado = areaCalculada * custoTotalM2;

  const parametros = {
    tipo_obra: tipoObra,
    padrao_obra: padraoObra,
    area_calculada: areaCalculada,
    multiplicador_regional: multiplicadorRegional,
    fator_complexidade: fatorComplexidade,
    fator_padrao: fatorPadrao,
    custo_m2_base: custoM2Base,
    custo_m2_ajustado: custoM2Ajustado,
    custo_m2_final: custoTotalM2,
    versao_calculo: "VALIDATE-BUDGET-RISK-1.0.0"
  };

  return {
    custoEstimado: parseFloat(custoEstimado.toFixed(2)),
    custoM2: parseFloat(custoTotalM2.toFixed(2)),
    parametros
  };
}

// ====================================
// 🎯 ANÁLISE DE RISCO
// ====================================

function analisarRisco(
  orcamentoUsuario: number,
  custoEstimado: number
): {
  percentage_difference: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  is_underbudgeted: boolean;
  suggested_minimum: number;
} {
  if (orcamentoUsuario <= 0) {
    throw new Error("Orçamento deve ser maior que zero");
  }

  const diferenca = custoEstimado - orcamentoUsuario;
  const percentualDiferenca = (diferenca / orcamentoUsuario) * 100;
  
  let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
  let isUnderbudgeted = false;

  if (percentualDiferenca <= 20) {
    // Orçamento adequado (diferença até 20%)
    riskLevel = 'low';
    isUnderbudgeted = false;
  } else if (percentualDiferenca <= 50) {
    // Risco médio (diferença de 20-50%)
    riskLevel = 'medium';
    isUnderbudgeted = true;
  } else if (percentualDiferenca <= 100) {
    // Risco alto (diferença de 50-100%)
    riskLevel = 'high';
    isUnderbudgeted = true;
  } else {
    // Risco crítico (diferença > 100%)
    riskLevel = 'critical';
    isUnderbudgeted = true;
  }

  // Sugestão: 90% da estimativa para dar margem de manobra
  const suggestedMinimum = custoEstimado * 0.9;

  return {
    percentage_difference: parseFloat(percentualDiferenca.toFixed(1)),
    risk_level: riskLevel,
    is_underbudgeted: isUnderbudgeted,
    suggested_minimum: parseFloat(suggestedMinimum.toFixed(2))
  };
}

// ====================================
// 🚀 EDGE FUNCTION HANDLER
// ====================================

serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🚨 Validate Budget Risk v1.0.0 - Início");

    // Validar método
    if (req.method !== "POST") {
      throw new Error("Método não permitido. Use POST.");
    }

    // Parse do request com tratamento de erro
    let requestData: RequestData;
    try {
      requestData = await req.json();
    } catch (jsonError) {
      throw new Error("JSON inválido no request body");
    }
    
    if (!requestData || typeof requestData !== 'object') {
      throw new Error("Request body deve ser um objeto JSON válido");
    }
    
    if (!requestData.dados_obra || !requestData.orcamento_usuario) {
      throw new Error("Dados da obra e orçamento do usuário são obrigatórios");
    }
    
    if (typeof requestData.orcamento_usuario !== 'number' || requestData.orcamento_usuario <= 0) {
      throw new Error("Orçamento deve ser um número maior que zero");
    }

    const { dados_obra, orcamento_usuario } = requestData;

    console.log("📊 Dados recebidos:", {
      tipo_obra: dados_obra.tipo_obra,
      area_total: dados_obra.area_total,
      orcamento_usuario: orcamento_usuario
    });

    // ===== VALIDAÇÕES =====
    if (orcamento_usuario <= 0) {
      throw new Error("Orçamento deve ser maior que zero");
    }

    if (!dados_obra.area_total || dados_obra.area_total <= 0) {
      throw new Error("Área total deve ser maior que zero");
    }

    // ===== CÁLCULO DA ESTIMATIVA =====
    const { custoEstimado, custoM2, parametros } = calcularEstimativaRapida(dados_obra);

    console.log("💰 Cálculo concluído:", {
      custo_estimado: custoEstimado,
      custo_m2: custoM2,
      orcamento_usuario: orcamento_usuario
    });

    // ===== ANÁLISE DE RISCO =====
    const riskAnalysis = analisarRisco(orcamento_usuario, custoEstimado);

    console.log("🚨 Análise de risco:", riskAnalysis);

    // ===== RESPONSE =====
    const responseData: ResponseData = {
      estimated_cost: custoEstimado,
      cost_per_m2: custoM2,
      calculation_params: {
        tipo_obra: parametros.tipo_obra,
        padrao_obra: parametros.padrao_obra,
        area_calculada: parametros.area_calculada,
        multiplicador_regional: parametros.multiplicador_regional,
        fator_complexidade: parametros.fator_complexidade,
        versao_calculo: parametros.versao_calculo
      },
      risk_analysis: riskAnalysis
    };

    console.log("✅ Validação concluída com sucesso");

    return new Response(
      JSON.stringify({
        success: true,
        message: `Análise de risco concluída - Nível: ${riskAnalysis.risk_level}`,
        data: responseData,
        debug: {
          versao: "VALIDATE-BUDGET-RISK-1.0.0",
          timestamp: new Date().toISOString(),
          calculation_time: "< 100ms",
          parametros_completos: parametros
        }
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );

  } catch (error: any) {
    console.error("❌ Erro na validação de orçamento:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error?.message || "Erro interno do servidor",
        debug: {
          versao: "VALIDATE-BUDGET-RISK-1.0.0",
          timestamp: new Date().toISOString(),
          error_type: error?.name || "UnknownError"
        }
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  }
});