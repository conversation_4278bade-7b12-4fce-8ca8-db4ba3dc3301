# 📦 Guia de Módulos - ObrasAI 3.0

## 📋 Visão Geral

Este guia consolida a documentação de todos os módulos principais do sistema ObrasAI.

---

## 🏗️ Módulo de Obras

### Funcionalidades
- **CRUD completo** de obras
- **Dashboard** com métricas em tempo real
- **Orçamento paramétrico** com SINAPI
- **Gestão de condomínios** integrada
- **Timeline** de progresso

### Hooks Principais
```typescript
// Hook principal
const { obras, isLoading, createObra, updateObra } = useObras()

// Hook de condomínio
const { condominios, unidades } = useObrasCondominio()
```

### Campos Essenciais
- Informações básicas (nome, tipo, endereço)
- Dados financeiros (orçamento, custo real)
- Métricas (área, unidades, andares)
- Status e progresso
- Integração com vendas

---

## 💰 Módulo de Orçamento com IA

### Funcionalidades
- **Cálculo automático** baseado em área e tipo
- **Integração SINAPI** oficial
- **IA para sugestões** personalizadas
- **Comparativo** entre orçamentos
- **Alertas de risco** orçamentário

### Components
```typescript
<WizardOrcamento
  obra={obra}
  onComplete={(orcamento) => handleSave(orcamento)}
/>

<OrcamentoRiskAlert
  riskAlert={alert}
  onAcceptSuggestion={(valor) => updateOrcamento(valor)}
/>
```

### Edge Functions
- `ai-calculate-budget`: Cálculo com IA
- `gerar-orcamento-parametrico`: Geração baseada em SINAPI
- `sugestao-orcamento-ia`: Análise DeepSeek

---

## 📄 Módulo de Contratos Inteligentes

### Funcionalidades
- **Templates pré-configurados** por tipo de obra
- **IA para geração** de cláusulas personalizadas
- **Versionamento** automático
- **Assinatura digital** integrada
- **Tracking de status**

### Fluxo
1. Seleção de template
2. Personalização com IA
3. Revisão e ajustes
4. Envio para assinatura
5. Acompanhamento

### API
```typescript
const { generateContract, sendForSignature } = useContratoAI()
```

---

## 💸 Módulo de Despesas

### Funcionalidades
- **Registro completo** de despesas
- **Categorização automática**
- **Anexo de notas fiscais**
- **Integração com fornecedores**
- **Relatórios detalhados**

### Validações
- CPF/CNPJ do fornecedor
- Valor mínimo/máximo
- Data dentro do período da obra
- Categoria obrigatória

---

## 👥 Módulo de Fornecedores

### Tipos
- **Pessoa Física** (PF)
- **Pessoa Jurídica** (PJ)

### Funcionalidades
- **Cadastro completo** com validação
- **Integração com Receita** (CNPJ)
- **Histórico de transações**
- **Avaliação e rating**
- **Documentos anexados**

### Hooks
```typescript
const { fornecedoresPF } = useFornecedoresPF()
const { fornecedoresPJ } = useFornecedoresPJ()
```

---

## 🏢 Módulo de Condomínios

### Funcionalidades
- **Gestão de unidades** virtualizadas
- **Cálculo automático** de custos por unidade
- **Dashboard específico** para condomínios
- **Relatórios personalizados**
- **Integração com vendas**

### Componentes
```typescript
<CondomínioFormSection
  obra={obra}
  onChange={(data) => updateCondominio(data)}
/>

<ListaUnidadesVirtualized
  unidades={unidades}
  onEdit={(unidade) => editUnidade(unidade)}
/>
```

---

## 💼 Módulo de Vendas

### Funcionalidades
- **Registro de vendas** de unidades
- **Cálculo de comissões**
- **Tracking de status**
- **Relatórios financeiros**
- **Integração com CRM**

### Campos
- Valor de venda
- Data da venda
- Status (negociação, fechado, cancelado)
- Comissão do corretor
- Despesas adicionais

---

## 🔧 Módulo SINAPI

### Funcionalidades
- **Base oficial** atualizada mensalmente
- **Busca semântica** com embeddings
- **Composições e insumos**
- **Cálculo de BDI**
- **Histórico de preços**

### Components
```typescript
<SinapiSelector
  onSelect={(item) => addToOrcamento(item)}
  categoria="ALVENARIA"
/>

<MonitoringSinapi
  showUpdates={true}
  showMaintenanceIndicator={true}
/>
```

---

## 📊 Módulo de Analytics

### Métricas
- **Leads**: Conversão e fontes
- **Usuários**: Engajamento e retenção
- **Financeiro**: MRR, ARR, LTV
- **Produto**: Uso de features

### API
```typescript
analytics.trackEvent('obra_created', { tipo: 'residencial' })
analytics.trackAIUsage('orcamento_gerado')
analytics.getAllMetrics()
```

---

## 🔐 Módulo de Segurança

### Features
- **Autenticação** multi-fator
- **Autorização** baseada em roles
- **Criptografia** de dados sensíveis
- **Audit log** completo
- **Tenant isolation**

### Implementação
- RLS em todas as tabelas
- Validação dupla (frontend + backend)
- Sanitização de inputs
- Rate limiting em APIs

---

## 🎯 Integrações Entre Módulos

### Obra → Orçamento
- Orçamento automático na criação
- Atualização de valores em tempo real

### Fornecedor → Despesa
- Seleção rápida de fornecedor
- Histórico de transações

### Condomínio → Venda
- Unidades disponíveis para venda
- Cálculo automático de preços

### Contrato → Obra
- Vinculação automática
- Status sincronizado

### SINAPI → Orçamento
- Preços atualizados
- Composições detalhadas