import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

// ====================================
// 🎯 INTERFACES
// ====================================

interface DadosObraReais {
  id: string;
  nome: string;
  tipo_projeto: string;
  area_total: number;
  localizacao: {
    endereco: string;
    estado: string;
    cidade?: string;
  };
  financeiro: {
    orcamento_atual: number;
    estimativa_sinapi: number;
    diferenca_percentual: number;
  };
  especificacoes: {
    padrao_obra?: string;
    tipo_condominio?: string;
  };
}

interface RequestData {
  dados_obra_reais: DadosObraReais;
}

interface AnaliseIA {
  problema_identificado: string;
  impacto_financeiro: string;
  sugestoes_praticas: string[];
  cronograma_acao: {
    item: string;
    prazo: string;
    prioridade: 'alta' | 'media' | 'baixa';
  }[];
  alternativas_viabilidade: string[];
  valor_recomendado_minimo: number;
  valor_recomendado_otimo: number;
  observacoes_tecnicas: string[];
}

// ====================================
// 🤖 FUNÇÃO PARA CHAMAR DEEPSEEK
// ====================================

async function chamarDeepSeekAnalise(dadosObra: DadosObraReais): Promise<AnaliseIA> {
  const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY');
  
  if (!DEEPSEEK_API_KEY) {
    throw new Error("DEEPSEEK_API_KEY não configurado");
  }

  // Construir prompt contextualizado com dados REAIS
  const prompt = `
Você é um especialista em orçamento e gestão de obras da construção civil no Brasil.

Analise esta situação REAL de uma obra com problemas orçamentários:

📋 DADOS REAIS DA OBRA:
- Nome: ${dadosObra.nome}
- Tipo: ${dadosObra.tipo_projeto}
- Área: ${dadosObra.area_total.toLocaleString()} m²
- Localização: ${dadosObra.localizacao.endereco}, ${dadosObra.localizacao.estado}
- Padrão: ${dadosObra.especificacoes.padrao_obra || 'Não informado'}
- Tipo Condomínio: ${dadosObra.especificacoes.tipo_condominio || 'Não aplicável'}

💰 SITUAÇÃO FINANCEIRA REAL:
- Orçamento do Cliente: R$ ${dadosObra.financeiro.orcamento_atual.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
- Estimativa SINAPI: R$ ${dadosObra.financeiro.estimativa_sinapi.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
- Diferença: ${dadosObra.financeiro.diferenca_percentual.toFixed(1)}% (orçamento abaixo da estimativa)

🎯 ANÁLISE SOLICITADA:
Forneça uma análise técnica e prática considerando:
1. Os custos reais de construção no estado de ${dadosObra.localizacao.estado}
2. A viabilidade financeira desta obra específica
3. Sugestões práticas e acionáveis para o proprietário
4. Cronograma de ações prioritárias
5. Alternativas de otimização baseadas na realidade do mercado

Responda EXCLUSIVAMENTE em formato JSON válido com esta estrutura:
{
  "problema_identificado": "Descrição clara do problema com base nos dados reais",
  "impacto_financeiro": "Explicação do impacto real desta diferença orçamentária",
  "sugestoes_praticas": [
    "Sugestão específica 1 baseada nos dados da obra",
    "Sugestão específica 2 considerando localização",
    "Sugestão específica 3 para o tipo de projeto"
  ],
  "cronograma_acao": [
    {
      "item": "Ação específica baseada na análise",
      "prazo": "Prazo realista (ex: 15 dias, 1 mês)",
      "prioridade": "alta" | "media" | "baixa"
    }
  ],
  "alternativas_viabilidade": [
    "Alternativa 1 considerando orçamento limitado",
    "Alternativa 2 para viabilizar projeto"
  ],
  "valor_recomendado_minimo": ${Math.round(dadosObra.financeiro.estimativa_sinapi * 0.8)},
  "valor_recomendado_otimo": ${Math.round(dadosObra.financeiro.estimativa_sinapi * 0.95)},
  "observacoes_tecnicas": [
    "Observação técnica específica para este projeto",
    "Consideração sobre mercado regional"
  ]
}

IMPORTANTE: 
- Use APENAS os dados reais fornecidos
- Considere especificidades do estado ${dadosObra.localizacao.estado}
- Seja prático e acionável
- Não invente dados ou valores
- Foque na viabilidade real do projeto
`;

  console.log("🤖 Enviando prompt para DeepSeek com dados reais da obra:", dadosObra.id);

  try {
    const response = await fetch("https://api.deepseek.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${DEEPSEEK_API_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "deepseek-chat",
        messages: [
          {
            role: "system",
            content: "Você é um especialista em orçamento de obras no Brasil. Responda sempre em JSON válido com análises técnicas precisas baseadas em dados reais do mercado."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        response_format: { type: "json_object" }
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Erro DeepSeek:", response.status, errorText);
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error("Resposta inválida da DeepSeek API");
    }

    const content = data.choices[0].message.content;
    const analise: AnaliseIA = JSON.parse(content);
    
    console.log("✅ Análise IA gerada com sucesso para obra:", dadosObra.nome);
    
    return analise;

  } catch (error: any) {
    console.error("❌ Erro na comunicação com DeepSeek:", error);
    throw new Error(`Falha na análise IA: ${error.message}`);
  }
}

// ====================================
// 🚀 EDGE FUNCTION HANDLER
// ====================================

serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    console.log("🎯 Sugestão Orçamento IA v1.0.0 - Início");

    // Validar método
    if (req.method !== "POST") {
      throw new Error("Método não permitido. Use POST.");
    }

    // Parse do request
    let requestData: RequestData;
    try {
      requestData = await req.json();
    } catch (jsonError) {
      throw new Error("JSON inválido no request body");
    }

    // Validar dados da obra
    const { dados_obra_reais } = requestData;
    
    if (!dados_obra_reais) {
      throw new Error("dados_obra_reais é obrigatório");
    }

    // Validações específicas dos dados reais
    if (!dados_obra_reais.id || !dados_obra_reais.nome) {
      throw new Error("ID e nome da obra são obrigatórios");
    }

    if (!dados_obra_reais.financeiro.orcamento_atual || !dados_obra_reais.financeiro.estimativa_sinapi) {
      throw new Error("Dados financeiros da obra são obrigatórios");
    }

    if (!dados_obra_reais.area_total || dados_obra_reais.area_total <= 0) {
      throw new Error("Área da obra deve ser maior que zero");
    }

    console.log("📊 Processando dados reais da obra:", {
      id: dados_obra_reais.id,
      nome: dados_obra_reais.nome,
      area: dados_obra_reais.area_total,
      estado: dados_obra_reais.localizacao.estado,
      diferenca: dados_obra_reais.financeiro.diferenca_percentual
    });

    // ===== ANÁLISE IA COM DADOS REAIS =====
    const analiseIA = await chamarDeepSeekAnalise(dados_obra_reais);

    console.log("✅ Análise IA concluída com sucesso");

    // ===== SALVAR LOG DA ANÁLISE (OPCIONAL) =====
    // TODO: Implementar salvamento do histórico de análises
    
    return new Response(
      JSON.stringify({
        success: true,
        message: "Análise IA gerada com sucesso",
        data: analiseIA,
        metadata: {
          obra_id: dados_obra_reais.id,
          obra_nome: dados_obra_reais.nome,
          timestamp: new Date().toISOString(),
          versao: "SUGESTAO-IA-1.0.0"
        }
      }),
      {
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );

  } catch (error: any) {
    console.error("❌ Erro na sugestão IA:", error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error?.message || "Erro interno do servidor",
        debug: {
          versao: "SUGESTAO-IA-1.0.0",
          timestamp: new Date().toISOString(),
          error_type: error?.name || "UnknownError"
        }
      }),
      {
        status: 400,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  }
});