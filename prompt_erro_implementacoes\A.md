[INFO] Dashboard do condomínio carregado com sucesso via RPC otimizada Object
anrphijuostbgbscxmzx.supabase.co/rest/v1/ai_usage_tracking?select=\*&user_id=eq.eab5beab-3012-42a8-9c9b-1b5f3595b0ff&date=eq.2025-08-02:1 Failed to load resource: the server responded with a status of 406 ()Understand this error
47b52cf9-46b0-4959-a211-363dff1cf081:1 Access to fetch at 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2' from origin 'http://localhost:8080' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.Understand this error
anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2:1 Failed to load resource: net::ERR_FAILEDUnderstand this error
aiApi.ts:104 Erro ao chamar Edge Function: FunctionsFetchError: Failed to send a request to the Edge Function
at @supabase_supabase-js.js?v=87503299:1390:17
sendMessage @ aiApi.ts:104Understand this error
47b52cf9-46b0-4959-a211-363dff1cf081:1 Access to fetch at 'https://anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2' from origin 'http://localhost:8080' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: No 'Access-Control-Allow-Origin' header is present on the requested resource.Understand this error
anrphijuostbgbscxmzx.supabase.co/functions/v1/ai-chat-handler-v2:1 Failed to load resource: net::ERR_FAILEDUnderstand this error
aiApi.ts:104 Erro ao chamar Edge Function: FunctionsFetchError: Failed to send a request to the Edge Function
at @supabase_supabase-js.js?v=87503299:1390:17
sendMessage @ aiApi.ts:104Understand this error
useSubscription.ts:93 🧪 Modo DEV: Usando plano pro
secure-logger.ts:171 [INFO] Tentando buscar dashboard do condomínio otimizado {condominioId: '2119b45e-a8db-4a76-81b9-7ca15f792399', tenantId: 'a88331a3-a339-4c5e-9cec-ee9479aaebdd'}
secure-logger.ts:171 [INFO] Dashboard do condomínio carregado com sucesso via RPC otimizada {condominioId: '2119b45e-a8db-4a76-81b9-7ca15f792399', dataReceived: true, tenantId: 'a88331a3-a339-4c5e-9cec-ee9479aaebdd'}
