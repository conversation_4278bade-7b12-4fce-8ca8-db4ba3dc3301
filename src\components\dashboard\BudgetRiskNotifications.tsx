/**
 * 🚨 Componente de Notificações de Risco Orçamentário
 * 
 * Exibe notificações persistentes no dashboard para obras com risco orçamentário.
 * Integra com o sistema de alertas existente e permite ações rápidas.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useQuery } from "@tanstack/react-query";
import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  ArrowRight,
  Building,
  Calculator,
  CheckCircle,
  DollarSign,
  Info,
  X
} from "lucide-react";
import React, { useState } from "react";
import { Link } from "react-router-dom";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useObras } from "@/hooks/useObras";
import { formatCurrencyBR } from "@/lib/i18n";
import { cn } from "@/lib/utils";
import type { Obra } from "@/types/api";

// ====================================
// 🎯 TIPOS E INTERFACES
// ====================================

interface BudgetRiskNotification {
  obra: Obra;
  riskLevel: 'medium' | 'high' | 'critical';
  percentageDifference: number;
  estimatedCost: number;
  userBudget: number;
  message: string;
}

// ====================================
// 🎨 CONFIGURAÇÕES VISUAIS
// ====================================

const getRiskConfig = (level: string) => {
  switch (level) {
    case 'medium':
      return {
        color: 'text-yellow-600 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-950/30',
        borderColor: 'border-yellow-200 dark:border-yellow-800',
        icon: Info,
        badgeVariant: 'secondary' as const,
        title: 'Atenção Necessária'
      };
    case 'high':
      return {
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-50 dark:bg-orange-950/30',
        borderColor: 'border-orange-200 dark:border-orange-800',
        icon: AlertTriangle,
        badgeVariant: 'secondary' as const,
        title: 'Risco Alto'
      };
    case 'critical':
      return {
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-950/30',
        borderColor: 'border-red-200 dark:border-red-800',
        icon: AlertTriangle,
        badgeVariant: 'destructive' as const,
        title: 'Risco Crítico'
      };
    default:
      return {
        color: 'text-slate-600 dark:text-slate-400',
        bgColor: 'bg-slate-50 dark:bg-slate-950/30',
        borderColor: 'border-slate-200 dark:border-slate-800',
        icon: Info,
        badgeVariant: 'secondary' as const,
        title: 'Informação'
      };
  }
};

// ====================================
// 🎯 COMPONENTE PRINCIPAL
// ====================================

export const BudgetRiskNotifications: React.FC = () => {
  const [dismissedNotifications, setDismissedNotifications] = useState<string[]>([]);
  
  const { data: obras, isLoading } = useObras();

  // Processar obras para identificar riscos
  const riskNotifications: BudgetRiskNotification[] = React.useMemo(() => {
    if (!obras) return [];

    return obras
      .filter(obra => {
        // Filtrar apenas obras com orçamento e estimativa paramétrica
        const orcamento = obra.orcamento_total || obra.orcamento || 0;
        const estimativa = obra.valor_orcamento_parametrico || 0;
        
        return (
          orcamento > 0 && 
          estimativa > 0 && 
          !dismissedNotifications.includes(obra.id)
        );
      })
      .map(obra => {
        const orcamento = obra.orcamento_total || obra.orcamento || 0;
        const estimativa = obra.valor_orcamento_parametrico || 0;
        const diferenca = estimativa - orcamento;
        const percentualDiferenca = (diferenca / orcamento) * 100;

        let riskLevel: 'medium' | 'high' | 'critical' | null = null;
        let message = '';

        // Usar a mesma lógica do AnaliseOrcamentariaInteligente
        if (percentualDiferenca > 20 && percentualDiferenca <= 50) {
          riskLevel = 'medium';
          message = `Estimativa IA é ${percentualDiferenca.toFixed(0)}% maior que orçamento disponível`;
        } else if (percentualDiferenca > 50 && percentualDiferenca <= 100) {
          riskLevel = 'high';
          message = `Estimativa IA é ${percentualDiferenca.toFixed(0)}% maior que orçamento disponível`;
        } else if (percentualDiferenca > 100) {
          riskLevel = 'critical';
          message = `CRÍTICO: Estimativa IA é ${percentualDiferenca.toFixed(0)}% maior que orçamento disponível`;
        }

        return riskLevel ? {
          obra,
          riskLevel,
          percentageDifference: percentualDiferenca,
          estimatedCost: estimativa,
          userBudget: orcamento,
          message
        } : null;
      })
      .filter((notification): notification is BudgetRiskNotification => notification !== null)
      .sort((a, b) => {
        // Ordenar por nível de risco (crítico primeiro)
        const riskOrder = { critical: 3, high: 2, medium: 1 };
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel];
      });
  }, [obras, dismissedNotifications]);

  const handleDismissNotification = (obraId: string) => {
    setDismissedNotifications(prev => [...prev, obraId]);
  };

  if (isLoading) {
    return (
      <Card className="border-slate-200 dark:border-slate-800">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Alertas de Risco Orçamentário
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
            <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (riskNotifications.length === 0) {
    return (
      <Card className="border-green-200 dark:border-green-800 bg-green-50/50 dark:bg-green-950/20">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2 text-green-700 dark:text-green-300">
            <CheckCircle className="h-4 w-4" />
            Situação Orçamentária
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-green-600 dark:text-green-400">
            ✅ Todas as obras estão com orçamentos adequados ou dentro da margem aceitável.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-amber-200 dark:border-amber-800">
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2 text-amber-700 dark:text-amber-300">
          <AlertTriangle className="h-4 w-4" />
          Alertas de Risco Orçamentário
          <Badge variant="secondary" className="ml-auto">
            {riskNotifications.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <AnimatePresence>
          {riskNotifications.slice(0, 3).map((notification, index) => {
            const config = getRiskConfig(notification.riskLevel);
            const IconComponent = config.icon;

            return (
              <motion.div
                key={notification.obra.id}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Alert className={cn(
                  "border-l-4 relative",
                  config.bgColor,
                  config.borderColor
                )}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1 min-w-0">
                      <IconComponent className={cn("h-4 w-4 mt-0.5", config.color)} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm truncate">
                            {notification.obra.nome}
                          </h4>
                          <Badge variant={config.badgeVariant} className="text-xs">
                            {config.title}
                          </Badge>
                        </div>
                        <AlertDescription className="text-xs text-muted-foreground mb-2">
                          {notification.message}
                        </AlertDescription>
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div>
                            <span className="text-muted-foreground">Seu orçamento:</span>
                            <div className="font-medium text-blue-600 dark:text-blue-400">
                              {formatCurrencyBR(notification.userBudget)}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Estimativa IA:</span>
                            <div className="font-medium text-purple-600 dark:text-purple-400">
                              {formatCurrencyBR(notification.estimatedCost)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        asChild
                        size="sm"
                        variant="ghost"
                        className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                      >
                        <Link to={`/dashboard/obras/${notification.obra.id}`}>
                          <ArrowRight className="h-3 w-3" />
                        </Link>
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDismissNotification(notification.obra.id)}
                        className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </Alert>
              </motion.div>
            );
          })}
        </AnimatePresence>

        {riskNotifications.length > 3 && (
          <div className="text-center pt-2">
            <Separator className="mb-3" />
            <Link
              to="/dashboard/obras"
              className="text-sm text-muted-foreground hover:text-foreground inline-flex items-center gap-1"
            >
              Ver mais {riskNotifications.length - 3} alertas
              <ArrowRight className="h-3 w-3" />
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BudgetRiskNotifications;