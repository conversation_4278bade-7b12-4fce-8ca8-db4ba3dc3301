# 🗄️ Arquitetura do Banco de Dados - ObrasAI 3.0

## 📋 Visão Geral

O ObrasAI utiliza PostgreSQL via Supabase com arquitetura multi-tenant, RLS (Row Level Security) e schema bem definido para garantir performance e segurança.

---

## 🏗️ Tabelas Principais

### 📊 obras
Tabela central do sistema, armazena todas as obras/projetos.

**Campos principais:**
- `id` (uuid) - PK
- `tenant_id` (uuid) - Isolamento por empresa
- `nome`, `tipo_projeto`, `endereco`, `cidade`, `estado`
- `orcamento`, `valor_orcamento_parametrico`
- `area_total`, `numero_unidades`, `numero_andares`
- `data_inicio`, `data_fim`, `progresso`
- `status_venda`, `valor_venda`, `comissao_corretor_percentual`

**Índices:**
- `idx_obras_tenant_id`
- `idx_obras_status`
- `idx_obras_tipo_projeto`

### 👥 profiles
Perfis de usuários com informações adicionais.

**Campos:**
- `id` (uuid) - FK para auth.users
- `full_name`, `company_name`, `phone`
- `role` (enum: admin, user, viewer)
- `tenant_id` - Empresa do usuário

### 💰 orcamentos
Orçamentos detalhados das obras.

**Campos:**
- `obra_id` - FK para obras
- `valor_total`, `margem_lucro`
- `parametros_calculo` (jsonb) - Configurações do cálculo
- `itens` (jsonb) - Detalhamento dos itens

### 📄 contratos
Contratos inteligentes com versionamento.

**Campos:**
- `obra_id`, `tipo_contrato`, `template_id`
- `conteudo` (text) - Conteúdo do contrato
- `status` (draft, sent, signed, cancelled)
- `versao`, `assinado_em`

### 🏢 fornecedores_pj / fornecedores_pf
Cadastro de fornecedores pessoa jurídica e física.

**Campos PJ:**
- `cnpj`, `razao_social`, `nome_fantasia`
- `inscricao_estadual`, `inscricao_municipal`

**Campos PF:**
- `cpf`, `nome_completo`, `rg`

### 💸 despesas
Registro de todas as despesas das obras.

**Campos:**
- `obra_id`, `fornecedor_id`, `tipo_fornecedor`
- `valor`, `data_despesa`, `categoria`
- `descricao`, `numero_nota_fiscal`

### 📊 sinapi_composicoes_analiticas
Base SINAPI oficial para orçamentos.

**Campos:**
- `codigo`, `descricao`, `unidade`
- `custo_total`, `origem_preco`
- `coeficientes` (jsonb) - Detalhamento

### 🤖 embeddings_conhecimento
Vetores para busca semântica inteligente.

**Campos:**
- `titulo`, `conteudo`
- `embedding` (vector) - Vetor de 1536 dimensões
- `metadata` (jsonb)

### 📈 analytics_events
Eventos de analytics e métricas.

**Campos:**
- `event_type`, `user_id`, `session_id`
- `properties` (jsonb) - Dados do evento
- `timestamp`

### 💳 subscriptions
Assinaturas e planos dos usuários.

**Campos:**
- `tenant_id`, `stripe_customer_id`
- `plan_type` (trial, pro, enterprise)
- `status`, `current_period_start/end`

---

## 🔐 Segurança (RLS Policies)

### Padrão de Isolamento por Tenant
Todas as tabelas principais têm políticas RLS:

```sql
-- Exemplo de policy
CREATE POLICY "tenant_isolation" ON obras
  FOR ALL USING (tenant_id = auth.jwt()->>'tenant_id');
```

### Políticas por Tabela
- **obras**: Isolamento por tenant + permissões por role
- **fornecedores**: Visualização compartilhada, edição isolada
- **contratos**: Acesso restrito por obra
- **analytics**: Apenas admins do tenant

---

## 🚀 Otimizações

### Índices Estratégicos
```sql
-- Busca por tipo e status
CREATE INDEX idx_obras_tipo_status ON obras(tipo_projeto, status);

-- Performance em joins
CREATE INDEX idx_despesas_obra_id ON despesas(obra_id);
CREATE INDEX idx_orcamentos_obra_id ON orcamentos(obra_id);
```

### Particionamento
- **analytics_events**: Particionado por mês
- **embeddings**: Índice HNSW para busca vetorial

### Views Materializadas
```sql
-- Dashboard metrics
CREATE MATERIALIZED VIEW mv_dashboard_metrics AS
  SELECT tenant_id, 
         COUNT(*) as total_obras,
         SUM(orcamento) as total_orcamento
  FROM obras
  GROUP BY tenant_id;
```

---

## 📊 Funções e Procedures

### create_condominio_project()
Cria projeto de condomínio com unidades.

### calculate_budget_parametric()
Calcula orçamento baseado em parâmetros.

### search_similar_embeddings()
Busca semântica usando pgvector.

### cleanup_old_analytics()
Limpeza automática de dados antigos.

---

## 🔄 Migrações

### Convenções
- Nomenclatura: `YYYYMMDDHHMMSS_description.sql`
- Sempre incluir DOWN migration
- Testar em ambiente de staging primeiro

### Exemplo
```sql
-- UP
ALTER TABLE obras ADD COLUMN bim_model_url TEXT;

-- DOWN  
ALTER TABLE obras DROP COLUMN bim_model_url;
```

---

## 📈 Monitoramento

### Queries Lentas
```sql
-- Identificar queries > 1s
SELECT query, mean_exec_time
FROM pg_stat_statements
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;
```

### Tamanho das Tabelas
```sql
SELECT relname, pg_size_pretty(pg_total_relation_size(relid))
FROM pg_stat_user_tables
ORDER BY pg_total_relation_size(relid) DESC;
```

### Índices Não Utilizados
```sql
SELECT indexrelname, idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan = 0;
```