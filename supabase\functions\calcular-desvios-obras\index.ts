import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { z } from "https://deno.land/x/zod@v3.23.8/mod.ts";

// 🔒 Headers CORS
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

// 📋 Schema de validação
const CalcularDesviosSchema = z.object({
  obra_id: z.string().uuid().optional(),
  recalcular_todos: z.boolean().optional().default(false),
});

// 🏗️ Interfaces
interface ObraComDados {
  id: string;
  nome: string;
  orcamento: number;
  tenant_id: string;
  orcamentos_parametricos?: {
    custo_estimado: number;
    id: string;
  }[];
  despesas?: {
    custo: number;
    categoria: string;
  }[];
}

interface AlertaDesvio {
  obra_id: string;
  tenant_id: string;
  usuario_id: string;
  tipo_alerta: "baixa" | "media" | "alta" | "critica";
  severidade: "baixa" | "media" | "alta" | "critica";
  titulo: string;
  descricao: string;
  valor_limite: number;
  valor_atual: number;
  percentual_desvio: number;
  status: "ativo";
}

// 🧮 Função para calcular status financeiro unificado
function calcularStatusFinanceiro(
  orcamentoDisponivel: number,
  totalGasto: number,
  estimativaParametrica?: number
): {
  statusFinanceiro: "saudavel" | "atencao" | "risco" | "critico";
  alertas: AlertaDesvio[];
  percentualConsumido: number;
  saldoDisponivel: number;
  desvioEstimativa?: number;
} {
  const saldoDisponivel = orcamentoDisponivel - totalGasto;
  const percentualConsumido = orcamentoDisponivel > 0 ? (totalGasto / orcamentoDisponivel) * 100 : 0;
  
  let statusFinanceiro: "saudavel" | "atencao" | "risco" | "critico" = "saudavel";
  const alertas: AlertaDesvio[] = [];
  
  // 🔴 Critério 1: Saldo disponível negativo (CRÍTICO)
  if (saldoDisponivel < 0) {
    statusFinanceiro = "critico";
  }
  // 🟠 Critério 2: Mais de 90% do orçamento consumido (RISCO)
  else if (percentualConsumido > 90) {
    statusFinanceiro = "risco";
  }
  // 🟡 Critério 3: Mais de 75% do orçamento consumido (ATENÇÃO)
  else if (percentualConsumido > 75) {
    statusFinanceiro = "atencao";
  }
  
  // 🔍 Critério 4: Comparação com estimativa paramétrica
  let desvioEstimativa: number | undefined;
  if (estimativaParametrica && estimativaParametrica > 0) {
    desvioEstimativa = ((estimativaParametrica - orcamentoDisponivel) / orcamentoDisponivel) * 100;
    
    // ⚠️ Se a estimativa é muito maior que o orçamento disponível
    if (desvioEstimativa > 100) {
      statusFinanceiro = "critico";
    } else if (desvioEstimativa > 50 && statusFinanceiro === "saudavel") {
      statusFinanceiro = "risco";
    } else if (desvioEstimativa > 20 && statusFinanceiro === "saudavel") {
      statusFinanceiro = "atencao";
    }
  }
  
  return {
    statusFinanceiro,
    alertas,
    percentualConsumido,
    saldoDisponivel,
    desvioEstimativa
  };
}

// 🏭 Função principal para calcular desvios
async function calcularDesvios(
  supabaseClient: any,
  obraId?: string,
  tenantId?: string
) {
  try {
    // 🔍 Buscar obras com dados relacionados
    let query = supabaseClient
      .from("obras")
      .select(`
        id,
        nome,
        orcamento,
        tenant_id,
        orcamentos_parametricos!inner(
          custo_estimado,
          id
        ),
        despesas(
          custo,
          categoria
        )
      `);
    
    if (obraId) {
      query = query.eq("id", obraId);
    }
    
    if (tenantId) {
      query = query.eq("tenant_id", tenantId);
    }
    
    const { data: obras, error } = await query;
    
    if (error) {
      throw new Error(`Erro ao buscar obras: ${error.message}`);
    }
    
    if (!obras || obras.length === 0) {
      return {
        success: true,
        message: "Nenhuma obra encontrada para calcular desvios",
        alertas_criados: 0
      };
    }
    
    const alertasParaCriar: AlertaDesvio[] = [];
    
    // 📊 Processar cada obra
    for (const obra of obras as ObraComDados[]) {
      const totalGasto = obra.despesas?.reduce((sum, d) => sum + d.custo, 0) || 0;
      const estimativaParametrica = obra.orcamentos_parametricos?.[0]?.custo_estimado;
      
      const analise = calcularStatusFinanceiro(
        obra.orcamento,
        totalGasto,
        estimativaParametrica
      );
      
      // 🚨 Criar alertas baseados no status
      if (analise.statusFinanceiro !== "saudavel") {
        // Alerta de orçamento consumido
        if (analise.percentualConsumido > 75) {
          const severidade = analise.percentualConsumido > 95 ? "critica" : 
                            analise.percentualConsumido > 90 ? "alta" : "media";
          
          alertasParaCriar.push({
            obra_id: obra.id,
            tenant_id: obra.tenant_id,
            usuario_id: obra.tenant_id, // Usando tenant_id como fallback
            tipo_alerta: "orcamento",
            severidade,
            titulo: `Orçamento ${analise.percentualConsumido.toFixed(1)}% consumido`,
            descricao: `A obra "${obra.nome}" já consumiu ${analise.percentualConsumido.toFixed(1)}% do orçamento disponível. Saldo restante: R$ ${analise.saldoDisponivel.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
            valor_limite: obra.orcamento,
            valor_atual: totalGasto,
            percentual_desvio: analise.percentualConsumido,
            status: "ativo"
          });
        }
        
        // Alerta de estimativa vs orçamento
        if (analise.desvioEstimativa && Math.abs(analise.desvioEstimativa) > 20) {
          const severidade = Math.abs(analise.desvioEstimativa) > 100 ? "critica" :
                            Math.abs(analise.desvioEstimativa) > 50 ? "alta" : "media";
          
          alertasParaCriar.push({
            obra_id: obra.id,
            tenant_id: obra.tenant_id,
            usuario_id: obra.tenant_id,
            tipo_alerta: "estimativa",
            severidade,
            titulo: `Desvio de ${analise.desvioEstimativa.toFixed(1)}% da estimativa`,
            descricao: `A estimativa paramétrica (R$ ${estimativaParametrica?.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}) está ${analise.desvioEstimativa > 0 ? 'acima' : 'abaixo'} do orçamento disponível (R$ ${obra.orcamento.toLocaleString('pt-BR', { minimumFractionDigits: 2 })})`,
            valor_limite: obra.orcamento,
            valor_atual: estimativaParametrica || 0,
            percentual_desvio: analise.desvioEstimativa,
            status: "ativo"
          });
        }
      }
    }
    
    // 💾 Limpar alertas antigos da mesma obra e inserir novos
    if (alertasParaCriar.length > 0) {
      // Deletar alertas existentes das obras processadas
      const obrasIds = [...new Set(alertasParaCriar.map(a => a.obra_id))];
      await supabaseClient
        .from("alertas_desvio")
        .delete()
        .in("obra_id", obrasIds);
      
      // Inserir novos alertas
      const { error: insertError } = await supabaseClient
        .from("alertas_desvio")
        .insert(alertasParaCriar);
      
      if (insertError) {
        throw new Error(`Erro ao inserir alertas: ${insertError.message}`);
      }
    }
    
    return {
      success: true,
      message: `Desvios calculados com sucesso para ${obras.length} obra(s)`,
      alertas_criados: alertasParaCriar.length,
      obras_processadas: obras.length
    };
    
  } catch (error) {
    console.error("Erro ao calcular desvios:", error);
    throw error;
  }
}

// 🚀 Servidor principal
serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }
  
  try {
    // 🔐 Cliente Supabase
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );
    
    // 📨 Validar requisição
    const body = await req.json();
    const validationResult = CalcularDesviosSchema.safeParse(body);
    
    if (!validationResult.success) {
      return new Response(
        JSON.stringify({
          success: false,
          error: "Dados de entrada inválidos",
          details: validationResult.error.flatten().fieldErrors,
        }),
        {
          status: 400,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
    
    const { obra_id, recalcular_todos } = validationResult.data;
    
    // 🔒 Autenticação (opcional para service role)
    const authHeader = req.headers.get("Authorization");
    let tenantId: string | undefined;
    
    if (authHeader) {
      const { data: { user }, error: authError } = await supabaseClient.auth.getUser(
        authHeader.replace("Bearer ", "")
      );
      
      if (!authError && user) {
        // Buscar tenant_id do usuário
        const { data: profile } = await supabaseClient
          .from("profiles")
          .select("tenant_id")
          .eq("id", user.id)
          .single();
        
        tenantId = profile?.tenant_id;
      }
    }
    
    // 🧮 Calcular desvios
    const resultado = await calcularDesvios(
      supabaseClient,
      recalcular_todos ? undefined : obra_id,
      tenantId
    );
    
    return new Response(
      JSON.stringify(resultado),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
    
  } catch (error) {
    console.error("Erro na function calcular-desvios-obras:", error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : "Erro interno do servidor",
      }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
});