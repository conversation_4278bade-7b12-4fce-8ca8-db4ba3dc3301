# 📚 Documentação ObrasAI 3.0

## 🎯 Visão Geral

Esta pasta contém toda a documentação técnica e estratégica do projeto ObrasAI - a plataforma mais avançada de gestão de obras com IA do Brasil.

---

## 📁 Estrutura da Documentação

### 📊 **Documentos Estratégicos**
- **CLAUDE.md** - 🤖 Diretrizes para desenvolvimento com IA (CRÍTICO)
- **ROADMAP_DOMINACAO_MERCADO.md** - 🚀 Plano para se tornar líder de mercado

### 📘 **Guias Téc<PERSON>** (`guides/`)
- **DEVELOPMENT_GUIDE.md** - Padrões de desenvolvimento e boas práticas
- **TECHNICAL_GUIDE.md** - Otimizações, TypeScript e performance  
- **TESTING_GUIDE.md** - Estratégias e ferramentas de teste
- **MODULES_GUIDE.md** - Documentação completa de todos os módulos

### 🏗️ **Arquitetura** (`architecture/`)
- **PROJECT_STRUCTURE.md** - Organização e estrutura do projeto
- **ERROR_HANDLING.md** - Sistema centralizado de tratamento de erros
- **DATABASE_SCHEMA.md** - Schema completo do banco PostgreSQL

### 🚀 **Features Principais** (`features/`)
- **AI_CHAT.md** - Sistema de chat inteligente com DeepSeek
- **SINAPI_INTEGRATION.md** - Integração com base oficial SINAPI
- **CONDOMINIO_MODULE.md** - Módulo especializado em condomínios
- **SMART_CONTRACTS.md** - Contratos inteligentes com IA

### 🔐 **Segurança** (`security/`)
- **security-report.md** - Relatório de segurança atual
- **CREDENTIAL_ROTATION_GUIDE.md** - Rotação segura de credenciais
- **security-improvements.md** - Melhorias implementadas
- **decryption-fixes.md** - Correções de criptografia

---

## 📖 **Documentação de Negócio (RAG/Embeddings)**

A documentação detalhada dos módulos (obras, fornecedores, orçamentos, contratos, etc.) está armazenada na base de conhecimento do Supabase como embeddings vetoriais.

**Total**: 360 chunks processados  
**Acesso**: Via chat IA integrado no sistema

### Como consultar:
```
💬 "Como criar uma obra?"
💬 "Como cadastrar fornecedor?"  
💬 "Como fazer orçamento paramétrico?"
💬 "Como usar SINAPI?"
```

---

## 🎯 **Próximos Passos**

1. **Implementar funcionalidades do ROADMAP** (BIM, ESG, Mobile, Banking)
2. **Manter documentação atualizada** conforme desenvolvimento
3. **Treinar novos desenvolvedores** usando os guias
4. **Expandir base de conhecimento** conforme novas features

---

## 💡 **Filosofia da Documentação**

- **Foco no essencial** - Apenas documentação que agrega valor
- **Sempre atualizada** - Documentação viva que evolui com o código
- **Orientada a resultados** - Facilita desenvolvimento e tomada de decisão
- **IA-First** - Documentação acessível via chat inteligente

---

## 📞 **Suporte**

Para dúvidas sobre a documentação:
1. **Consulte primeiro** o chat IA do sistema
2. **Verifique** os guias técnicos relevantes
3. **Atualize** a documentação quando necessário

---

*"Documentação é código que ensina. Deve ser clara, concisa e sempre útil."*